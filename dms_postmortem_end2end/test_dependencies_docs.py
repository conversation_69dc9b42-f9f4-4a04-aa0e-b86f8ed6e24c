#!/usr/bin/env python3
"""
依赖和文档测试模块
测试requirements.txt和README.md文件的存在和内容
"""

import unittest
import os
import subprocess
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


class TestDependenciesAndDocs(unittest.TestCase):
    """依赖和文档测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.base_dir = os.path.dirname(os.path.abspath(__file__))
        self.requirements_file = os.path.join(self.base_dir, "requirements.txt")
        self.readme_file = os.path.join(self.base_dir, "README.md")
    
    def test_requirements_file_exists(self):
        """测试requirements.txt文件存在"""
        self.assertTrue(os.path.exists(self.requirements_file))
    
    def test_requirements_file_content(self):
        """测试requirements.txt文件内容"""
        if not os.path.exists(self.requirements_file):
            self.skipTest("requirements.txt不存在")
        
        with open(self.requirements_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查必要的依赖包
        required_packages = [
            'opencv-python',
            'numpy',
            'requests',
            'rich',
            'Pillow',
            'psutil'
        ]
        
        for package in required_packages:
            self.assertIn(package, content, f"缺少必要依赖包: {package}")
    
    def test_readme_file_exists(self):
        """测试README.md文件存在"""
        self.assertTrue(os.path.exists(self.readme_file))
    
    def test_readme_file_content(self):
        """测试README.md文件内容"""
        if not os.path.exists(self.readme_file):
            self.skipTest("README.md不存在")
        
        with open(self.readme_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查必要的文档章节（考虑emoji）
        required_sections = [
            "# DMS端到端自动化系统",
            "功能特性",
            "系统要求",
            "安装配置",
            "使用指南",
            "配置文件",
            "架构设计",
            "测试",
            "故障排除"
        ]
        
        for section in required_sections:
            self.assertIn(section, content, f"缺少必要文档章节: {section}")
    
    def test_pip_install_requirements(self):
        """测试pip安装requirements.txt（仅验证语法）"""
        if not os.path.exists(self.requirements_file):
            self.skipTest("requirements.txt不存在")
        
        # 使用pip check来验证requirements.txt语法
        try:
            result = subprocess.run(
                ["pip", "install", "--dry-run", "-r", self.requirements_file],
                capture_output=True,
                text=True,
                timeout=60
            )
            # 即使dry-run失败也可能是正常的（如包不存在），主要检查语法
            self.assertIsNotNone(result)
        except subprocess.TimeoutExpired:
            self.skipTest("pip命令超时")
        except FileNotFoundError:
            self.skipTest("pip命令不可用")
    
    def test_main_controller_version(self):
        """测试主控脚本版本命令"""
        main_script = os.path.join(self.base_dir, "main_controller.py")
        
        if not os.path.exists(main_script):
            self.skipTest("main_controller.py不存在")
        
        try:
            result = subprocess.run(
                ["python", main_script, "--version"],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            self.assertEqual(result.returncode, 0)
            self.assertIn("version", result.stdout.lower())
        except subprocess.TimeoutExpired:
            self.fail("版本命令超时")
        except Exception as e:
            self.fail(f"版本命令执行失败: {e}")


if __name__ == '__main__':
    unittest.main(verbosity=2)
