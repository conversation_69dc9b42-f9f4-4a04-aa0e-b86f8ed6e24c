#!/usr/bin/env python3
"""
系统日志器模块
提供结构化日志记录、错误追踪、执行报告生成功能
"""

import os
import json
import logging
import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path
import traceback


class SystemLogger:
    """系统日志器类 - 提供结构化日志和报告功能"""
    
    def __init__(self, log_file: str = "dms_end2end.log", log_level: str = "INFO"):
        """
        初始化系统日志器
        
        Args:
            log_file: 日志文件路径
            log_level: 日志级别
        """
        self.log_file = log_file
        self.log_level = getattr(logging, log_level.upper(), logging.INFO)
        
        # 初始化日志记录
        self.steps_log = []  # 步骤日志
        self.errors_log = []  # 错误日志
        self.start_time = datetime.datetime.now()
        
        # 设置Python logging
        self._setup_logging()
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("系统日志器初始化完成")
    
    def _setup_logging(self) -> None:
        """设置Python logging配置"""
        # 创建日志目录
        log_dir = os.path.dirname(self.log_file) if os.path.dirname(self.log_file) else "."
        os.makedirs(log_dir, exist_ok=True)
        
        # 配置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 文件处理器
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(self.log_level)
        file_handler.setFormatter(formatter)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.log_level)
        console_handler.setFormatter(formatter)
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)
        
        # 清除现有处理器并添加新的
        root_logger.handlers.clear()
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
    
    def log_step(self, step_name: str, status: str, details: str = "") -> None:
        """
        记录执行步骤
        
        Args:
            step_name: 步骤名称
            status: 状态 (START, SUCCESS, FAILED, WARNING)
            details: 详细信息
        """
        timestamp = datetime.datetime.now()
        
        step_entry = {
            "timestamp": timestamp.isoformat(),
            "step_name": step_name,
            "status": status,
            "details": details,
            "duration": None
        }
        
        # 如果是同一步骤的结束状态，计算持续时间
        if status in ["SUCCESS", "FAILED"] and self.steps_log:
            for i in range(len(self.steps_log) - 1, -1, -1):
                if (self.steps_log[i]["step_name"] == step_name and 
                    self.steps_log[i]["status"] == "START"):
                    start_time = datetime.datetime.fromisoformat(self.steps_log[i]["timestamp"])
                    duration = (timestamp - start_time).total_seconds()
                    step_entry["duration"] = duration
                    break
        
        self.steps_log.append(step_entry)
        
        # 记录到Python logging
        log_message = f"[{step_name}] {status}"
        if details:
            log_message += f" - {details}"
        if step_entry["duration"]:
            log_message += f" (耗时: {step_entry['duration']:.2f}秒)"
        
        if status == "FAILED":
            self.logger.error(log_message)
        elif status == "WARNING":
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
    
    def log_error(self, error_info: Any, context: str = "") -> None:
        """
        记录错误信息
        
        Args:
            error_info: 错误信息（可以是字符串、异常对象等）
            context: 错误上下文
        """
        timestamp = datetime.datetime.now()
        
        # 处理不同类型的错误信息
        if isinstance(error_info, Exception):
            error_message = str(error_info)
            error_type = type(error_info).__name__
            error_traceback = traceback.format_exc()
        elif isinstance(error_info, str):
            error_message = error_info
            error_type = "UserError"
            error_traceback = None
        else:
            error_message = str(error_info)
            error_type = "UnknownError"
            error_traceback = None
        
        error_entry = {
            "timestamp": timestamp.isoformat(),
            "error_type": error_type,
            "error_message": error_message,
            "context": context,
            "traceback": error_traceback
        }
        
        self.errors_log.append(error_entry)
        
        # 记录到Python logging
        log_message = f"错误: {error_message}"
        if context:
            log_message = f"[{context}] {log_message}"
        
        self.logger.error(log_message)
        if error_traceback:
            self.logger.error(f"堆栈跟踪:\n{error_traceback}")
    
    def generate_report(self) -> str:
        """
        生成执行报告
        
        Returns:
            格式化的报告字符串
        """
        end_time = datetime.datetime.now()
        total_duration = (end_time - self.start_time).total_seconds()
        
        # 统计信息
        total_steps = len(self.steps_log)
        successful_steps = len([s for s in self.steps_log if s["status"] == "SUCCESS"])
        failed_steps = len([s for s in self.steps_log if s["status"] == "FAILED"])
        warning_steps = len([s for s in self.steps_log if s["status"] == "WARNING"])
        total_errors = len(self.errors_log)
        
        # 生成报告
        report_lines = [
            "=" * 80,
            "DMS端到端自动化系统执行报告",
            "=" * 80,
            f"开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"总耗时: {total_duration:.2f}秒",
            "",
            "执行统计:",
            f"  总步骤数: {total_steps}",
            f"  成功步骤: {successful_steps}",
            f"  失败步骤: {failed_steps}",
            f"  警告步骤: {warning_steps}",
            f"  错误总数: {total_errors}",
            "",
        ]
        
        # 步骤详情
        if self.steps_log:
            report_lines.extend([
                "步骤执行详情:",
                "-" * 40
            ])
            
            for step in self.steps_log:
                duration_str = f" ({step['duration']:.2f}s)" if step['duration'] else ""
                status_symbol = {
                    "START": "🔄",
                    "SUCCESS": "✅",
                    "FAILED": "❌",
                    "WARNING": "⚠️"
                }.get(step["status"], "❓")
                
                report_lines.append(
                    f"{status_symbol} [{step['timestamp'][:19]}] {step['step_name']} - {step['status']}{duration_str}"
                )
                if step["details"]:
                    report_lines.append(f"    详情: {step['details']}")
            
            report_lines.append("")
        
        # 错误详情
        if self.errors_log:
            report_lines.extend([
                "错误详情:",
                "-" * 40
            ])
            
            for i, error in enumerate(self.errors_log, 1):
                report_lines.extend([
                    f"错误 #{i}:",
                    f"  时间: {error['timestamp'][:19]}",
                    f"  类型: {error['error_type']}",
                    f"  消息: {error['error_message']}"
                ])
                if error["context"]:
                    report_lines.append(f"  上下文: {error['context']}")
                if error["traceback"]:
                    report_lines.append(f"  堆栈跟踪: {error['traceback'][:200]}...")
                report_lines.append("")
        
        # 总结
        if failed_steps == 0 and total_errors == 0:
            status_summary = "✅ 执行成功"
        elif failed_steps > 0:
            status_summary = f"❌ 执行失败 ({failed_steps}个步骤失败)"
        else:
            status_summary = f"⚠️ 执行完成但有错误 ({total_errors}个错误)"
        
        report_lines.extend([
            "=" * 80,
            f"执行结果: {status_summary}",
            "=" * 80
        ])
        
        report_content = "\n".join(report_lines)
        
        # 保存报告到文件
        report_file = f"execution_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            self.logger.info(f"执行报告已保存到: {report_file}")
        except Exception as e:
            self.logger.error(f"保存报告失败: {e}")
        
        return report_content
    
    def get_step_summary(self) -> Dict[str, Any]:
        """
        获取步骤执行摘要
        
        Returns:
            步骤摘要字典
        """
        summary = {
            "total_steps": len(self.steps_log),
            "successful_steps": len([s for s in self.steps_log if s["status"] == "SUCCESS"]),
            "failed_steps": len([s for s in self.steps_log if s["status"] == "FAILED"]),
            "warning_steps": len([s for s in self.steps_log if s["status"] == "WARNING"]),
            "total_errors": len(self.errors_log),
            "start_time": self.start_time.isoformat(),
            "current_time": datetime.datetime.now().isoformat()
        }
        return summary
    
    def export_logs_json(self, filename: str = None) -> str:
        """
        导出日志为JSON格式
        
        Args:
            filename: 输出文件名，如果为None则自动生成
            
        Returns:
            输出文件路径
        """
        if filename is None:
            filename = f"logs_export_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        export_data = {
            "metadata": {
                "export_time": datetime.datetime.now().isoformat(),
                "start_time": self.start_time.isoformat(),
                "log_file": self.log_file
            },
            "summary": self.get_step_summary(),
            "steps": self.steps_log,
            "errors": self.errors_log
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"日志已导出到: {filename}")
            return filename
        except Exception as e:
            self.logger.error(f"导出日志失败: {e}")
            raise


if __name__ == "__main__":
    # 简单测试
    logger = SystemLogger()
    
    logger.log_step("TEST", "START", "开始测试")
    logger.log_step("TEST", "SUCCESS", "测试完成")
    logger.log_error("这是一个测试错误", "测试上下文")
    
    print(logger.generate_report())
