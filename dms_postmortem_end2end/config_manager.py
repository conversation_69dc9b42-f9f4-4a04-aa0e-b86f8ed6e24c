#!/usr/bin/env python3
"""
配置管理器模块
负责加载、验证和管理所有配置文件
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path


class ConfigManager:
    """配置管理器类 - 统一管理所有配置文件"""
    
    def __init__(self, config_file: str = "end2end_config.json", base_dir: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 主配置文件路径
            base_dir: 基础目录，如果为None则使用当前脚本目录
        """
        self.base_dir = base_dir or os.path.dirname(os.path.abspath(__file__))
        self.config_file = os.path.join(self.base_dir, config_file)
        self.config = {}
        self.ip_port_config = {}
        self.calibration_config = {}
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 加载主配置
        self._load_main_config()
    
    def _load_main_config(self) -> None:
        """加载主配置文件"""
        try:
            if not os.path.exists(self.config_file):
                raise FileNotFoundError(f"主配置文件不存在: {self.config_file}")
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            self.logger.info(f"成功加载主配置文件: {self.config_file}")
            
        except Exception as e:
            self.logger.error(f"加载主配置文件失败: {e}")
            raise
    
    def load_ip_port_config(self) -> Dict[str, Any]:
        """
        加载IP端口配置
        
        Returns:
            IP端口配置字典
        """
        try:
            ip_port_path = self._get_absolute_path(self.config["paths"]["ip_port_config"])
            
            if not os.path.exists(ip_port_path):
                raise FileNotFoundError(f"IP端口配置文件不存在: {ip_port_path}")
            
            with open(ip_port_path, 'r', encoding='utf-8') as f:
                self.ip_port_config = json.load(f)
            
            self.logger.info(f"成功加载IP端口配置: {ip_port_path}")
            return self.ip_port_config
            
        except Exception as e:
            self.logger.error(f"加载IP端口配置失败: {e}")
            raise
    
    def load_calibration_config(self) -> Dict[str, Any]:
        """
        加载校准配置
        
        Returns:
            校准配置字典
        """
        try:
            cali_path = self._get_absolute_path(self.config["paths"]["calibration_config"])
            
            if not os.path.exists(cali_path):
                raise FileNotFoundError(f"校准配置文件不存在: {cali_path}")
            
            with open(cali_path, 'r', encoding='utf-8') as f:
                self.calibration_config = json.load(f)
            
            self.logger.info(f"成功加载校准配置: {cali_path}")
            return self.calibration_config
            
        except Exception as e:
            self.logger.error(f"加载校准配置失败: {e}")
            raise
    
    def validate_configs(self) -> bool:
        """
        验证所有配置的有效性
        
        Returns:
            配置是否有效
        """
        try:
            # 验证主配置
            if not self._validate_main_config():
                return False
            
            # 加载并验证IP端口配置
            ip_port_config = self.load_ip_port_config()
            if not self._validate_ip_port_config(ip_port_config):
                return False
            
            # 加载并验证校准配置
            calibration_config = self.load_calibration_config()
            if not self._validate_calibration_config(calibration_config):
                return False
            
            # 验证C++程序路径
            cpp_path = self.get_cpp_program_path()
            if not os.path.exists(cpp_path):
                self.logger.error(f"C++程序不存在: {cpp_path}")
                return False
            
            self.logger.info("所有配置验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def get_cpp_program_path(self) -> str:
        """
        获取C++程序的完整路径
        
        Returns:
            C++程序路径
        """
        cpp_config = self.config["cpp_program"]
        cpp_dir = self._get_absolute_path(cpp_config["directory"])
        executable = cpp_config["executable"]
        
        # 首先尝试指定目录
        cpp_path = os.path.join(cpp_dir, executable)
        if os.path.exists(cpp_path):
            return cpp_path
        
        # 回退到fallback目录
        fallback_dir = self._get_absolute_path(cpp_config["fallback_directory"])
        fallback_path = os.path.join(fallback_dir, executable)
        if os.path.exists(fallback_path):
            return fallback_path
        
        # 如果都不存在，返回主路径（让调用者处理错误）
        return cpp_path
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键如 "remote_service.ip"
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def _get_absolute_path(self, path: str) -> str:
        """
        获取绝对路径
        
        Args:
            path: 相对或绝对路径
            
        Returns:
            绝对路径
        """
        if os.path.isabs(path):
            return path
        return os.path.join(self.base_dir, path)
    
    def _validate_main_config(self) -> bool:
        """验证主配置"""
        required_sections = ["remote_service", "cpp_program", "processing", "paths"]
        
        for section in required_sections:
            if section not in self.config:
                self.logger.error(f"主配置缺少必需的节: {section}")
                return False
        
        return True
    
    def _validate_ip_port_config(self, config: Dict[str, Any]) -> bool:
        """验证IP端口配置"""
        required_fields = ["ip", "port"]
        
        for field in required_fields:
            if field not in config:
                self.logger.error(f"IP端口配置缺少必需字段: {field}")
                return False
        
        # 验证端口范围
        port = config["port"]
        if not isinstance(port, int) or port < 1 or port > 65535:
            self.logger.error(f"无效的端口号: {port}")
            return False
        
        return True
    
    def _validate_calibration_config(self, config: Dict[str, Any]) -> bool:
        """验证校准配置"""
        required_fields = [
            "head_yaw", "head_pitch", "head_roll",
            "left_eye_yaw", "left_eye_pitch",
            "right_eye_yaw", "right_eye_pitch",
            "left_eye_curve_mean", "right_eye_curve_mean"
        ]
        
        for field in required_fields:
            if field not in config:
                self.logger.error(f"校准配置缺少必需字段: {field}")
                return False
            
            # 验证数值类型
            if not isinstance(config[field], (int, float)):
                self.logger.error(f"校准配置字段类型错误: {field}")
                return False
        
        return True


if __name__ == "__main__":
    # 简单测试
    logging.basicConfig(level=logging.INFO)
    
    try:
        cm = ConfigManager()
        print("配置管理器初始化成功")
        
        if cm.validate_configs():
            print("所有配置验证通过")
        else:
            print("配置验证失败")
            
    except Exception as e:
        print(f"测试失败: {e}")
