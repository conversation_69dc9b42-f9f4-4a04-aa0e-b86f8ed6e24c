#!/usr/bin/env python3
"""
端到端处理器模块
整合视频裁剪和DMS处理流程，支持数据传递和状态管理
"""

import os
import subprocess
import tempfile
import shutil
from typing import Dict, Any, List, Optional
import logging
from pathlib import Path


class EndToEndProcessor:
    """端到端处理器类 - 整合视频裁剪和DMS处理流程"""
    
    def __init__(self, config_manager, logger):
        """
        初始化端到端处理器
        
        Args:
            config_manager: 配置管理器实例
            logger: 系统日志器实例
        """
        self.config_manager = config_manager
        self.logger = logger
        self.system_logger = logging.getLogger(__name__)
        
        # 从配置获取处理参数
        self.processing_config = config_manager.get_config("processing", {})
        self.video_cutting_config = config_manager.get_config("video_cutting", {})
        self.dms_processing_config = config_manager.get_config("dms_processing", {})
        
        # 设置工作目录
        self.temp_dir = self.processing_config.get("temp_dir", "./temp")
        self.output_dir = self.processing_config.get("output_dir", "./output")
        self.cleanup_temp = self.processing_config.get("cleanup_temp", True)
        
        # 确保目录存在
        os.makedirs(self.temp_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)
        
        self.system_logger.info("端到端处理器初始化完成")
    
    def execute_video_cutting(self, params: Dict[str, Any]) -> List[str]:
        """
        执行视频裁剪
        
        Args:
            params: 裁剪参数字典，包含:
                - input_video: 输入视频路径
                - time_ranges: 时间范围字符串
                - roi: ROI区域字符串
                - output_dir: 输出目录
                
        Returns:
            裁剪后的视频文件路径列表
        """
        try:
            self.logger.log_step("VIDEO_CUTTING", "START", "开始视频裁剪")
            
            # 验证输入参数
            required_params = ["input_video", "time_ranges", "output_dir"]
            for param in required_params:
                if param not in params:
                    raise ValueError(f"缺少必需参数: {param}")
            
            input_video = params["input_video"]
            time_ranges = params["time_ranges"]
            roi = params.get("roi", None)
            output_dir = params["output_dir"]
            
            # 验证输入文件存在
            if not os.path.exists(input_video):
                raise FileNotFoundError(f"输入视频文件不存在: {input_video}")
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 构建视频裁剪命令
            cut_script_path = os.path.join(
                os.path.dirname(os.path.abspath(__file__)),
                "cut_video_fromwhatyouwant_optimized.py"
            )
            
            if not os.path.exists(cut_script_path):
                raise FileNotFoundError(f"视频裁剪脚本不存在: {cut_script_path}")
            
            # 构建命令参数
            cmd = [
                "python3", cut_script_path,
                "-i", input_video,
                "-o", output_dir,
                "-t", time_ranges
            ]
            
            # 添加ROI参数
            if roi:
                cmd.extend(["--roi", roi])
            
            # 添加配置参数
            threads = self.processing_config.get("max_threads", 0)
            if threads > 0:
                cmd.extend(["--threads", str(threads)])
            
            self.system_logger.info(f"执行视频裁剪命令: {' '.join(cmd)}")
            
            # 执行视频裁剪
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=3600  # 1小时超时
            )
            
            if result.returncode != 0:
                error_msg = f"视频裁剪失败: {result.stderr}"
                self.logger.log_error(error_msg, "VIDEO_CUTTING")
                raise RuntimeError(error_msg)
            
            # 解析输出，获取生成的文件列表
            output_files = self._parse_cutting_output(result.stdout, output_dir)
            
            if not output_files:
                raise RuntimeError("视频裁剪未生成任何输出文件")
            
            self.logger.log_step("VIDEO_CUTTING", "SUCCESS", f"成功裁剪 {len(output_files)} 个视频片段")
            return output_files
            
        except Exception as e:
            self.logger.log_error(e, "VIDEO_CUTTING")
            self.logger.log_step("VIDEO_CUTTING", "FAILED", str(e))
            raise
    
    def execute_dms_processing(self, video_files: List[str]) -> str:
        """
        执行DMS处理
        
        Args:
            video_files: 输入视频文件路径列表
            
        Returns:
            处理后的输出视频路径
        """
        try:
            self.logger.log_step("DMS_PROCESSING", "START", f"开始DMS处理 {len(video_files)} 个视频文件")
            
            if not video_files:
                raise ValueError("没有输入视频文件")
            
            # 验证所有输入文件存在
            for video_file in video_files:
                if not os.path.exists(video_file):
                    raise FileNotFoundError(f"输入视频文件不存在: {video_file}")
            
            # 构建DMS处理命令
            dms_script_path = os.path.join(
                os.path.dirname(os.path.abspath(__file__)),
                "dms_postmortem_optimised.py"
            )
            
            if not os.path.exists(dms_script_path):
                raise FileNotFoundError(f"DMS处理脚本不存在: {dms_script_path}")
            
            # 构建输入参数字符串
            # 格式: /path/to/video1.mkv,[:],...
            input_str_parts = []
            for video_file in video_files:
                input_str_parts.extend([video_file, "[:]"])
            input_str = ",".join(input_str_parts[:-1])  # 移除最后一个 [:]
            
            # 生成输出文件名
            output_video = os.path.join(
                self.output_dir,
                f"dms_processed_{len(video_files)}_segments.mp4"
            )
            
            # 构建命令
            cmd = [
                "python3", dms_script_path,
                input_str,
                output_video
            ]
            
            self.system_logger.info(f"执行DMS处理命令: {' '.join(cmd)}")
            
            # 执行DMS处理
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=7200  # 2小时超时
            )
            
            if result.returncode != 0:
                error_msg = f"DMS处理失败: {result.stderr}"
                self.logger.log_error(error_msg, "DMS_PROCESSING")
                raise RuntimeError(error_msg)
            
            # 验证输出文件生成
            if not os.path.exists(output_video):
                raise RuntimeError(f"DMS处理未生成输出文件: {output_video}")
            
            self.logger.log_step("DMS_PROCESSING", "SUCCESS", f"DMS处理完成，输出: {output_video}")
            return output_video
            
        except Exception as e:
            self.logger.log_error(e, "DMS_PROCESSING")
            self.logger.log_step("DMS_PROCESSING", "FAILED", str(e))
            raise
    
    def execute_full_pipeline(self, input_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行完整的端到端流程
        
        Args:
            input_params: 输入参数字典
            
        Returns:
            处理结果字典
        """
        try:
            self.logger.log_step("FULL_PIPELINE", "START", "开始端到端处理流程")
            
            # 第一步：视频裁剪
            cut_video_files = self.execute_video_cutting(input_params)
            
            # 第二步：DMS处理
            final_output = self.execute_dms_processing(cut_video_files)
            
            # 清理临时文件
            if self.cleanup_temp:
                self._cleanup_temp_files(cut_video_files)
            
            # 构建结果
            result = {
                "success": True,
                "input_video": input_params.get("input_video"),
                "cut_segments": len(cut_video_files),
                "final_output": final_output,
                "temp_files_cleaned": self.cleanup_temp
            }
            
            self.logger.log_step("FULL_PIPELINE", "SUCCESS", f"端到端处理完成，输出: {final_output}")
            return result
            
        except Exception as e:
            self.logger.log_error(e, "FULL_PIPELINE")
            self.logger.log_step("FULL_PIPELINE", "FAILED", str(e))
            
            # 返回失败结果
            result = {
                "success": False,
                "error": str(e),
                "input_video": input_params.get("input_video"),
                "final_output": None
            }
            return result
    
    def _parse_cutting_output(self, stdout: str, output_dir: str) -> List[str]:
        """
        解析视频裁剪输出，提取生成的文件列表
        
        Args:
            stdout: 命令输出
            output_dir: 输出目录
            
        Returns:
            生成的文件路径列表
        """
        output_files = []
        
        # 查找输出目录中的视频文件
        if os.path.exists(output_dir):
            for file in os.listdir(output_dir):
                if file.lower().endswith(('.mp4', '.mkv', '.avi', '.mov')):
                    file_path = os.path.join(output_dir, file)
                    output_files.append(file_path)
        
        # 按文件名排序，确保处理顺序一致
        output_files.sort()
        
        return output_files
    
    def _cleanup_temp_files(self, temp_files: List[str]) -> None:
        """
        清理临时文件
        
        Args:
            temp_files: 要清理的临时文件列表
        """
        try:
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    self.system_logger.debug(f"已删除临时文件: {temp_file}")
            
            self.system_logger.info(f"已清理 {len(temp_files)} 个临时文件")
            
        except Exception as e:
            self.logger.log_error(e, "CLEANUP")
            self.system_logger.warning(f"清理临时文件时发生错误: {e}")


if __name__ == "__main__":
    # 简单测试
    import sys
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    from config_manager import ConfigManager
    from system_logger import SystemLogger
    
    try:
        cm = ConfigManager()
        sl = SystemLogger()
        processor = EndToEndProcessor(cm, sl)
        
        print("端到端处理器初始化成功")
        
    except Exception as e:
        print(f"测试失败: {e}")
