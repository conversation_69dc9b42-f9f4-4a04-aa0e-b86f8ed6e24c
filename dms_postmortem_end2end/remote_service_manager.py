#!/usr/bin/env python3
"""
远端服务管理器模块
负责远端服务的状态检查、配置验证和服务管理
"""

import socket
import time
import logging
import subprocess
import json
from typing import Dict, Any, Optional, Tuple
import requests
from requests.exceptions import RequestException, ConnectionError, Timeout


class RemoteServiceManager:
    """远端服务管理器类 - 管理远端服务的连接和状态"""
    
    def __init__(self, config_manager):
        """
        初始化远端服务管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # 从配置获取服务信息
        self.service_config = config_manager.get_config("remote_service", {})
        self.ip = self.service_config.get("ip", "***********")
        self.port = self.service_config.get("port", 1180)
        self.timeout = self.service_config.get("timeout", 30)
        self.max_retries = self.service_config.get("max_retries", 3)
        self.retry_delay = self.service_config.get("retry_delay", 5)
        self.health_endpoint = self.service_config.get("health_check_endpoint", "/health")
        
        self.logger.info(f"初始化远端服务管理器: {self.ip}:{self.port}")
    
    def check_service_status(self) -> bool:
        """
        检查远端服务状态
        
        Returns:
            服务是否可用
        """
        try:
            self.logger.info(f"检查远端服务状态: {self.ip}:{self.port}")
            
            # 首先进行TCP连接测试
            if not self._check_tcp_connection():
                self.logger.warning("TCP连接失败")
                return False
            
            # 如果TCP连接成功，尝试HTTP健康检查
            if self.health_endpoint:
                return self._check_http_health()
            
            # 如果没有配置健康检查端点，TCP连接成功就认为服务可用
            return True
            
        except Exception as e:
            self.logger.error(f"检查服务状态时发生异常: {e}")
            return False
    
    def validate_model_config(self) -> bool:
        """
        验证远端服务的模型配置是否与本地预期一致
        
        Returns:
            配置是否一致
        """
        try:
            self.logger.info("验证远端服务模型配置")
            
            # 首先检查服务是否可用
            if not self.check_service_status():
                self.logger.error("服务不可用，无法验证配置")
                return False
            
            # 获取本地配置
            local_ip_config = self.config_manager.load_ip_port_config()
            local_cali_config = self.config_manager.load_calibration_config()
            
            # 验证IP端口配置一致性
            if (local_ip_config.get("ip") != self.ip or 
                local_ip_config.get("port") != self.port):
                self.logger.error("IP端口配置不一致")
                return False
            
            # 这里可以扩展更多的配置验证逻辑
            # 例如通过API获取远端配置并比较
            
            self.logger.info("模型配置验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"验证模型配置时发生异常: {e}")
            return False
    
    def start_service_if_needed(self) -> bool:
        """
        如果服务未启动则启动服务
        注意：避免重复启动
        
        Returns:
            服务是否成功启动或已经运行
        """
        try:
            self.logger.info("检查是否需要启动服务")
            
            # 首先检查服务是否已经运行
            if self.check_service_status():
                self.logger.info("服务已经运行，无需启动")
                return True
            
            # 服务未运行，尝试启动
            self.logger.info("服务未运行，尝试启动...")
            
            # 这里可以实现具体的服务启动逻辑
            # 例如通过SSH连接到远端机器启动服务
            # 或者通过特定的API调用启动服务
            
            # 目前只是模拟启动过程
            success = self._attempt_service_startup()
            
            if success:
                # 等待服务启动完成
                self.logger.info("等待服务启动完成...")
                time.sleep(10)  # 给服务一些启动时间
                
                # 验证服务是否成功启动
                if self.check_service_status():
                    self.logger.info("服务启动成功")
                    return True
                else:
                    self.logger.error("服务启动失败")
                    return False
            else:
                self.logger.error("无法启动服务")
                return False
                
        except Exception as e:
            self.logger.error(f"启动服务时发生异常: {e}")
            return False
    
    def get_service_health(self) -> Dict[str, Any]:
        """
        获取服务健康状态详细信息
        
        Returns:
            服务健康状态字典
        """
        health_info = {
            "status": "unknown",
            "ip": self.ip,
            "port": self.port,
            "tcp_connection": False,
            "http_health": False,
            "response_time": None,
            "error_message": None
        }
        
        try:
            start_time = time.time()
            
            # TCP连接测试
            health_info["tcp_connection"] = self._check_tcp_connection()
            
            # HTTP健康检查
            if health_info["tcp_connection"] and self.health_endpoint:
                health_info["http_health"] = self._check_http_health()
            
            health_info["response_time"] = time.time() - start_time
            
            # 确定整体状态
            if health_info["tcp_connection"]:
                if self.health_endpoint:
                    health_info["status"] = "healthy" if health_info["http_health"] else "degraded"
                else:
                    health_info["status"] = "healthy"
            else:
                health_info["status"] = "unhealthy"
                
        except Exception as e:
            health_info["status"] = "error"
            health_info["error_message"] = str(e)
            self.logger.error(f"获取服务健康状态时发生异常: {e}")
        
        return health_info
    
    def _check_tcp_connection(self) -> bool:
        """检查TCP连接"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            result = sock.connect_ex((self.ip, self.port))
            sock.close()
            return result == 0
        except Exception as e:
            self.logger.debug(f"TCP连接检查失败: {e}")
            return False
    
    def _check_http_health(self) -> bool:
        """检查HTTP健康状态"""
        try:
            url = f"http://{self.ip}:{self.port}{self.health_endpoint}"
            response = requests.get(url, timeout=self.timeout)
            return response.status_code == 200
        except Exception as e:
            self.logger.debug(f"HTTP健康检查失败: {e}")
            return False
    
    def _attempt_service_startup(self) -> bool:
        """
        尝试启动服务
        这是一个模拟实现，实际应用中需要根据具体环境实现
        
        Returns:
            启动是否成功
        """
        try:
            # 这里可以实现具体的启动逻辑
            # 例如：
            # 1. SSH到远端机器执行启动命令
            # 2. 调用特定的API启动服务
            # 3. 使用容器编排工具启动服务
            
            self.logger.info("模拟服务启动过程...")
            
            # 目前只是返回False，表示无法自动启动
            # 在实际环境中，这里应该实现真正的启动逻辑
            return False
            
        except Exception as e:
            self.logger.error(f"启动服务时发生异常: {e}")
            return False


if __name__ == "__main__":
    # 简单测试
    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    from config_manager import ConfigManager
    
    logging.basicConfig(level=logging.INFO)
    
    try:
        cm = ConfigManager()
        rsm = RemoteServiceManager(cm)
        
        print("远端服务管理器初始化成功")
        
        status = rsm.check_service_status()
        print(f"服务状态: {status}")
        
        health = rsm.get_service_health()
        print(f"服务健康信息: {health}")
        
    except Exception as e:
        print(f"测试失败: {e}")
