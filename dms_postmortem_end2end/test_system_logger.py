#!/usr/bin/env python3
"""
系统日志器测试模块
测试结构化日志记录、错误追踪、执行报告生成功能
"""

import unittest
import os
import tempfile
import json
from unittest.mock import patch, MagicMock
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from system_logger import SystemLogger


class TestSystemLogger(unittest.TestCase):
    """系统日志器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.logger = None
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_system_logger_initialization(self):
        """测试系统日志器初始化"""
        log_file = os.path.join(self.temp_dir, "test.log")
        self.logger = SystemLogger(log_file=log_file)

        self.assertIsNotNone(self.logger)
        self.assertEqual(self.logger.log_file, log_file)
        self.assertEqual(len(self.logger.steps_log), 0)
        self.assertEqual(len(self.logger.errors_log), 0)

    def test_log_step(self):
        """测试步骤日志记录"""
        log_file = os.path.join(self.temp_dir, "test.log")
        logger = SystemLogger(log_file=log_file)

        logger.log_step("TEST_STEP", "START", "开始测试步骤")
        logger.log_step("TEST_STEP", "SUCCESS", "测试步骤完成")

        self.assertEqual(len(logger.steps_log), 2)
        self.assertEqual(logger.steps_log[0]["step_name"], "TEST_STEP")
        self.assertEqual(logger.steps_log[0]["status"], "START")
        self.assertEqual(logger.steps_log[1]["status"], "SUCCESS")
        self.assertIsNotNone(logger.steps_log[1]["duration"])  # 应该计算了持续时间

    def test_log_error(self):
        """测试错误日志记录"""
        log_file = os.path.join(self.temp_dir, "test.log")
        logger = SystemLogger(log_file=log_file)

        # 测试字符串错误
        logger.log_error("测试错误信息", "测试上下文")

        # 测试异常错误
        try:
            raise ValueError("测试异常")
        except ValueError as e:
            logger.log_error(e, "异常上下文")

        self.assertEqual(len(logger.errors_log), 2)
        self.assertEqual(logger.errors_log[0]["error_message"], "测试错误信息")
        self.assertEqual(logger.errors_log[0]["context"], "测试上下文")
        self.assertEqual(logger.errors_log[1]["error_type"], "ValueError")

    def test_generate_report(self):
        """测试报告生成"""
        log_file = os.path.join(self.temp_dir, "test.log")
        logger = SystemLogger(log_file=log_file)

        # 添加一些测试数据
        logger.log_step("STEP1", "START", "步骤1开始")
        logger.log_step("STEP1", "SUCCESS", "步骤1完成")
        logger.log_error("测试错误", "错误上下文")

        report = logger.generate_report()

        self.assertIsInstance(report, str)
        self.assertIn("DMS端到端自动化系统执行报告", report)
        self.assertIn("STEP1", report)
        self.assertIn("测试错误", report)
        self.assertIn("执行结果", report)


if __name__ == '__main__':
    unittest.main(verbosity=2)
