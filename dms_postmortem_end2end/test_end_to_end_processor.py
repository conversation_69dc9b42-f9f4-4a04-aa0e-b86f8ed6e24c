#!/usr/bin/env python3
"""
端到端处理器测试模块
测试视频裁剪和DMS处理流程的整合功能
"""

import unittest
import os
import tempfile
import json
from unittest.mock import patch, MagicMock
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_manager import ConfigManager
from system_logger import SystemLogger

from end_to_end_processor import EndToEndProcessor


class TestEndToEndProcessor(unittest.TestCase):
    """端到端处理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = None
        self.logger = None
        self.processor = None
        self._create_test_config()
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def _create_test_config(self):
        """创建测试配置"""
        # 创建目录结构
        cpp_kits_dir = os.path.join(self.temp_dir, "cpp_kits")
        os.makedirs(cpp_kits_dir, exist_ok=True)
        
        # 创建IP端口配置
        ip_port_config = {"ip": "***********", "port": 1180}
        with open(os.path.join(cpp_kits_dir, "ip_port.json"), 'w') as f:
            json.dump(ip_port_config, f)
        
        # 创建校准配置
        cali_config = {
            "head_yaw": 28.45, "head_pitch": 26.88, "head_roll": 2.68,
            "left_eye_yaw": 6.19, "left_eye_pitch": 8.57,
            "right_eye_yaw": 13.67, "right_eye_pitch": 6.75,
            "left_eye_curve_mean": 0.003146, "right_eye_curve_mean": 0.002944
        }
        with open(os.path.join(cpp_kits_dir, "calidata.json"), 'w') as f:
            json.dump(cali_config, f)
        
        # 创建主配置
        main_config = {
            "remote_service": {
                "ip": "***********", "port": 1180,
                "health_check_endpoint": "/health", "timeout": 30
            },
            "cpp_program": {
                "directory": "./sc3e_r_cpprelated", 
                "executable": "test_dms_internal_postmortem", 
                "fallback_directory": "."
            },
            "processing": {
                "temp_dir": "./temp", "output_dir": "./output",
                "cleanup_temp": True, "max_threads": 0, "desired_fps": 10
            },
            "video_cutting": {
                "ffmpeg_preset": "medium", "ffmpeg_crf": 23,
                "max_memory_usage": 80, "output_format": "mp4"
            },
            "dms_processing": {
                "image_roi_region": [0, 0, 1280, 800],
                "video_size": [1920, 1080], "show_eye_flag": True
            },
            "paths": {
                "ip_port_config": "./cpp_kits/ip_port.json",
                "calibration_config": "./cpp_kits/calidata.json"
            }
        }
        with open(os.path.join(self.temp_dir, "end2end_config.json"), 'w') as f:
            json.dump(main_config, f)
    
    def test_end_to_end_processor_initialization(self):
        """测试端到端处理器初始化"""
        config_file = os.path.join(self.temp_dir, "end2end_config.json")
        config_manager = ConfigManager(config_file, self.temp_dir)
        logger = SystemLogger()
        self.processor = EndToEndProcessor(config_manager, logger)

        self.assertIsNotNone(self.processor)
        self.assertEqual(self.processor.config_manager, config_manager)
        self.assertEqual(self.processor.logger, logger)
    
    def test_execute_video_cutting(self):
        """测试视频裁剪执行"""
        config_file = os.path.join(self.temp_dir, "end2end_config.json")
        config_manager = ConfigManager(config_file, self.temp_dir)
        logger = SystemLogger()
        processor = EndToEndProcessor(config_manager, logger)

        # 测试缺少必需参数的情况
        params = {
            "time_ranges": "00:04:57-00:05:17",
            "roi": "1920:1080:0:0",
            "output_dir": "/path/to/output"
        }

        with self.assertRaises(ValueError) as context:
            processor.execute_video_cutting(params)

        self.assertIn("缺少必需参数", str(context.exception))
    
    def test_execute_dms_processing(self):
        """测试DMS处理执行"""
        config_file = os.path.join(self.temp_dir, "end2end_config.json")
        config_manager = ConfigManager(config_file, self.temp_dir)
        logger = SystemLogger()
        processor = EndToEndProcessor(config_manager, logger)

        # 测试空视频文件列表
        with self.assertRaises(ValueError) as context:
            processor.execute_dms_processing([])

        self.assertIn("没有输入视频文件", str(context.exception))

    def test_execute_full_pipeline(self):
        """测试完整流程执行"""
        config_file = os.path.join(self.temp_dir, "end2end_config.json")
        config_manager = ConfigManager(config_file, self.temp_dir)
        logger = SystemLogger()
        processor = EndToEndProcessor(config_manager, logger)

        # 测试不存在的输入文件
        input_params = {
            "input_video": "/path/to/nonexistent.mkv",
            "time_ranges": "00:04:57-00:05:17",
            "roi": "1920:1080:0:0",
            "output_dir": "/path/to/output"
        }

        result = processor.execute_full_pipeline(input_params)
        self.assertFalse(result["success"])
        self.assertIn("error", result)


if __name__ == '__main__':
    unittest.main(verbosity=2)
