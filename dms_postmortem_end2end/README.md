# DMS端到端自动化系统

一个整合视频裁剪和DMS处理的端到端自动化解决方案，提供完整的工作流程管理、远端服务验证和详细的执行日志。

## 🚀 功能特性

- **🎬 智能视频裁剪**: 支持多时间段裁剪、ROI区域选择、多线程处理
- **🔍 DMS智能分析**: 集成DMS处理流程，支持人脸检测、眼部追踪等功能
- **🌐 远端服务管理**: 自动检查和验证远端服务状态，确保配置一致性
- **📊 详细日志记录**: 结构化日志、执行报告、错误追踪
- **⚙️ 灵活配置管理**: 统一的配置文件管理，支持多环境配置
- **🛡️ 健壮错误处理**: 完善的异常处理和恢复机制

## 📋 系统要求

### 软件依赖
- Python 3.8+
- FFmpeg (用于视频处理)
- OpenCV
- 相关C++程序和库

### 硬件建议
- 内存: 8GB+ (推荐16GB+)
- 存储: 足够的临时存储空间
- CPU: 多核处理器 (支持并行处理)

## 🔧 安装配置

### 1. 克隆项目
```bash
git clone <repository-url>
cd dms_postmortem_end2end
```

### 2. 安装Python依赖
```bash
pip install -r requirements.txt
```

### 3. 安装FFmpeg
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install ffmpeg

# CentOS/RHEL
sudo yum install ffmpeg

# macOS
brew install ffmpeg
```

### 4. 配置系统
复制并编辑配置文件：
```bash
cp end2end_config.json.example end2end_config.json
# 编辑配置文件以匹配您的环境
```

### 5. 验证安装
```bash
python main_controller.py --version
```

## 📖 使用指南

### 基本用法
```bash
python main_controller.py \
  --input-video /path/to/video.mkv \
  --time-ranges "00:04:57-00:05:17;00:15:10-00:15:30" \
  --output-dir /path/to/output
```

### 高级用法
```bash
# 带ROI裁剪
python main_controller.py \
  --input-video /path/to/video.mkv \
  --time-ranges "00:04:57-00:05:17" \
  --roi "1920:1080:0:0" \
  --output-dir /path/to/output

# 跳过服务检查
python main_controller.py \
  --input-video /path/to/video.mkv \
  --time-ranges "00:04:57-00:05:17" \
  --skip-service-check

# 自定义配置和日志
python main_controller.py \
  --input-video /path/to/video.mkv \
  --time-ranges "00:04:57-00:05:17" \
  --config custom_config.json \
  --log-level DEBUG \
  --log-file custom.log
```

### 参数说明
- `--input-video, -i`: 输入视频文件路径
- `--time-ranges, -t`: 时间范围，格式: "start1-end1;start2-end2"
- `--output-dir, -o`: 输出目录路径
- `--roi`: 感兴趣区域，格式: width:height:x:y
- `--config, -c`: 配置文件路径
- `--skip-service-check`: 跳过远端服务检查
- `--require-service`: 要求远端服务必须可用
- `--log-level`: 日志级别 (DEBUG/INFO/WARNING/ERROR)

## ⚙️ 配置文件

主配置文件 `end2end_config.json` 包含以下主要部分：

```json
{
  "remote_service": {
    "ip": "***********",
    "port": 1180,
    "timeout": 30
  },
  "cpp_program": {
    "directory": "./sc3e_r_cpprelated",
    "executable": "test_dms_internal_postmortem"
  },
  "processing": {
    "temp_dir": "./temp",
    "output_dir": "./output",
    "cleanup_temp": true
  }
}
```

## 🏗️ 架构设计

### 核心模块
- **ConfigManager**: 配置管理器，统一管理所有配置文件
- **RemoteServiceManager**: 远端服务管理器，负责服务状态检查和验证
- **SystemLogger**: 系统日志器，提供结构化日志和报告功能
- **EndToEndProcessor**: 端到端处理器，整合视频裁剪和DMS处理流程
- **MainController**: 主控脚本，提供统一的命令行接口

### 工作流程
1. **初始化**: 加载配置，初始化各模块
2. **预检查**: 验证配置，检查远端服务状态
3. **视频裁剪**: 根据时间范围和ROI裁剪视频
4. **DMS处理**: 调用DMS程序处理裁剪后的视频
5. **结果输出**: 生成最终视频和执行报告

## 🧪 测试

运行所有测试：
```bash
python -m pytest test_*.py -v
```

运行特定模块测试：
```bash
python test_config_manager.py
python test_remote_service.py
python test_system_logger.py
python test_end_to_end_processor.py
python test_main_controller.py
```

## 📊 日志和报告

系统会自动生成：
- **执行日志**: `dms_end2end.log`
- **执行报告**: `execution_report_YYYYMMDD_HHMMSS.txt`
- **JSON日志导出**: `logs_export_YYYYMMDD_HHMMSS.json`

## 🔍 故障排除

### 常见问题

1. **FFmpeg未找到**
   ```
   解决方案: 确保FFmpeg已安装并在PATH中
   ```

2. **远端服务连接失败**
   ```
   解决方案: 检查网络连接和服务配置，或使用 --skip-service-check
   ```

3. **C++程序执行失败**
   ```
   解决方案: 检查C++程序路径和依赖库配置
   ```

4. **内存不足**
   ```
   解决方案: 调整max_memory_usage配置或增加系统内存
   ```

### 调试模式
```bash
python main_controller.py \
  --log-level DEBUG \
  --input-video /path/to/video.mkv \
  --time-ranges "00:04:57-00:05:17"
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：
- 创建 Issue
- 发送邮件至 <EMAIL>
- 查看文档: [项目文档](docs/)

---

**版本**: 1.0.0  
**最后更新**: 2025-01-17
