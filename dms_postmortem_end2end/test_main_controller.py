#!/usr/bin/env python3
"""
主控脚本测试模块
测试统一命令行接口和模块协调功能
"""

import unittest
import os
import tempfile
import json
import subprocess
from unittest.mock import patch, MagicMock
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


class TestMainController(unittest.TestCase):
    """主控脚本测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.main_script = os.path.join(
            os.path.dirname(os.path.abspath(__file__)),
            "main_controller.py"
        )
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_main_controller_exists(self):
        """测试主控脚本文件存在"""
        # 现在应该存在
        self.assertTrue(os.path.exists(self.main_script))
    
    def test_main_controller_help(self):
        """测试主控脚本帮助信息"""
        if not os.path.exists(self.main_script):
            self.skipTest("main_controller.py不存在")
        
        # 测试帮助命令
        result = subprocess.run(
            ["python", self.main_script, "--help"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        self.assertEqual(result.returncode, 0)
        self.assertIn("usage", result.stdout.lower())
    
    def test_main_controller_version(self):
        """测试主控脚本版本信息"""
        if not os.path.exists(self.main_script):
            self.skipTest("main_controller.py不存在")
        
        # 测试版本命令
        result = subprocess.run(
            ["python", self.main_script, "--version"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        self.assertEqual(result.returncode, 0)
        self.assertIn("version", result.stdout.lower())
    
    def test_main_controller_invalid_args(self):
        """测试主控脚本无效参数处理"""
        if not os.path.exists(self.main_script):
            self.skipTest("main_controller.py不存在")
        
        # 测试无效参数
        result = subprocess.run(
            ["python", self.main_script, "--invalid-arg"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        self.assertNotEqual(result.returncode, 0)


if __name__ == '__main__':
    unittest.main(verbosity=2)
