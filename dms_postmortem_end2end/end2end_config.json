{"remote_service": {"ip": "***********", "port": 1180, "health_check_endpoint": "/health", "timeout": 30, "max_retries": 3, "retry_delay": 5}, "cpp_program": {"directory": "./sc3e_r_cpprelated", "executable": "test_dms_internal_postmortem", "fallback_directory": ".", "environment_vars": {"LD_LIBRARY_PATH": ""}}, "processing": {"temp_dir": "./temp", "output_dir": "./output", "cleanup_temp": true, "max_threads": 0, "desired_fps": 10}, "video_cutting": {"ffmpeg_preset": "medium", "ffmpeg_crf": 23, "max_memory_usage": 80, "update_interval": 0.5, "supported_formats": [".mp4", ".mkv", ".avi", ".mov", ".wmv", ".flv"], "output_format": "mp4", "hardware_acceleration": "auto"}, "dms_processing": {"image_roi_region": [0, 0, 1280, 800], "algo_expected_frame_size": [1280, 800], "video_size": [1920, 1080], "release_tmp_source": true, "show_eye_flag": true, "print_cpp_info": true}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "dms_end2end.log", "max_size": "10MB", "backup_count": 5}, "paths": {"ip_port_config": "./cpp_kits/ip_port.json", "calibration_config": "./cpp_kits/calidata.json", "cpp_kits_dir": "./cpp_kits"}}