#!/usr/bin/env python3
"""
配置管理器测试模块
测试配置文件加载、验证和管理功能
"""

import unittest
import os
import json
import tempfile
from unittest.mock import patch, mock_open
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_manager import ConfigManager


class TestConfigManager(unittest.TestCase):
    """配置管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = None
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_config_manager_initialization(self):
        """测试配置管理器初始化"""
        # 创建临时配置文件
        config_data = {
            "remote_service": {"ip": "***********", "port": 1180},
            "cpp_program": {"directory": "./sc3e_r_cpprelated", "executable": "test_dms_internal_postmortem", "fallback_directory": "."},
            "processing": {"temp_dir": "./temp"},
            "paths": {
                "ip_port_config": "./cpp_kits/ip_port.json",
                "calibration_config": "./cpp_kits/calidata.json"
            }
        }

        config_file = os.path.join(self.temp_dir, "end2end_config.json")
        with open(config_file, 'w') as f:
            json.dump(config_data, f)

        # 测试初始化
        self.config_manager = ConfigManager(config_file, self.temp_dir)
        self.assertIsNotNone(self.config_manager)
        self.assertEqual(self.config_manager.config["remote_service"]["ip"], "***********")
    
    def test_load_ip_port_config(self):
        """测试IP端口配置加载"""
        # 创建测试配置文件
        ip_port_config = {
            "ip": "***********",
            "port": 1180
        }

        # 创建目录结构
        cpp_kits_dir = os.path.join(self.temp_dir, "cpp_kits")
        os.makedirs(cpp_kits_dir, exist_ok=True)

        ip_port_file = os.path.join(cpp_kits_dir, "ip_port.json")
        with open(ip_port_file, 'w') as f:
            json.dump(ip_port_config, f)

        # 创建主配置
        main_config = {
            "remote_service": {"ip": "***********", "port": 1180},
            "cpp_program": {"directory": "./sc3e_r_cpprelated", "executable": "test_dms_internal_postmortem", "fallback_directory": "."},
            "processing": {"temp_dir": "./temp"},
            "paths": {
                "ip_port_config": "./cpp_kits/ip_port.json",
                "calibration_config": "./cpp_kits/calidata.json"
            }
        }

        config_file = os.path.join(self.temp_dir, "end2end_config.json")
        with open(config_file, 'w') as f:
            json.dump(main_config, f)

        # 测试加载
        config_manager = ConfigManager(config_file, self.temp_dir)
        result = config_manager.load_ip_port_config()

        self.assertEqual(result["ip"], "***********")
        self.assertEqual(result["port"], 1180)
    
    def test_load_calibration_config(self):
        """测试校准配置加载"""
        cali_config = {
            "head_yaw": 28.45,
            "head_pitch": 26.88,
            "head_roll": 2.68,
            "left_eye_yaw": 6.19,
            "left_eye_pitch": 8.57,
            "right_eye_yaw": 13.67,
            "right_eye_pitch": 6.75,
            "left_eye_curve_mean": 0.003146,
            "right_eye_curve_mean": 0.002944
        }

        # 创建目录结构
        cpp_kits_dir = os.path.join(self.temp_dir, "cpp_kits")
        os.makedirs(cpp_kits_dir, exist_ok=True)

        cali_file = os.path.join(cpp_kits_dir, "calidata.json")
        with open(cali_file, 'w') as f:
            json.dump(cali_config, f)

        # 创建主配置
        main_config = {
            "remote_service": {"ip": "***********", "port": 1180},
            "cpp_program": {"directory": "./sc3e_r_cpprelated", "executable": "test_dms_internal_postmortem", "fallback_directory": "."},
            "processing": {"temp_dir": "./temp"},
            "paths": {
                "ip_port_config": "./cpp_kits/ip_port.json",
                "calibration_config": "./cpp_kits/calidata.json"
            }
        }

        config_file = os.path.join(self.temp_dir, "end2end_config.json")
        with open(config_file, 'w') as f:
            json.dump(main_config, f)

        # 测试加载
        config_manager = ConfigManager(config_file, self.temp_dir)
        result = config_manager.load_calibration_config()

        self.assertEqual(result["head_yaw"], 28.45)
        self.assertEqual(result["left_eye_yaw"], 6.19)
    
    def test_validate_configs(self):
        """测试配置验证"""
        # 创建完整的测试环境
        self._create_test_environment()

        config_file = os.path.join(self.temp_dir, "end2end_config.json")
        config_manager = ConfigManager(config_file, self.temp_dir)

        # 由于C++程序不存在，验证应该失败
        result = config_manager.validate_configs()
        self.assertFalse(result)

    def test_get_cpp_program_path(self):
        """测试C++程序路径获取"""
        # 创建测试配置
        main_config = {
            "remote_service": {"ip": "***********", "port": 1180},
            "cpp_program": {
                "directory": "./sc3e_r_cpprelated",
                "executable": "test_dms_internal_postmortem",
                "fallback_directory": "."
            },
            "processing": {"temp_dir": "./temp"},
            "paths": {
                "ip_port_config": "./cpp_kits/ip_port.json",
                "calibration_config": "./cpp_kits/calidata.json"
            }
        }

        config_file = os.path.join(self.temp_dir, "end2end_config.json")
        with open(config_file, 'w') as f:
            json.dump(main_config, f)

        config_manager = ConfigManager(config_file, self.temp_dir)
        result = config_manager.get_cpp_program_path()

        # 应该返回主路径（即使文件不存在）
        expected_path = os.path.join(self.temp_dir, ".", "sc3e_r_cpprelated", "test_dms_internal_postmortem")
        self.assertEqual(result, expected_path)

    def _create_test_environment(self):
        """创建完整的测试环境"""
        # 创建目录结构
        cpp_kits_dir = os.path.join(self.temp_dir, "cpp_kits")
        os.makedirs(cpp_kits_dir, exist_ok=True)

        # 创建IP端口配置
        ip_port_config = {"ip": "***********", "port": 1180}
        with open(os.path.join(cpp_kits_dir, "ip_port.json"), 'w') as f:
            json.dump(ip_port_config, f)

        # 创建校准配置
        cali_config = {
            "head_yaw": 28.45, "head_pitch": 26.88, "head_roll": 2.68,
            "left_eye_yaw": 6.19, "left_eye_pitch": 8.57,
            "right_eye_yaw": 13.67, "right_eye_pitch": 6.75,
            "left_eye_curve_mean": 0.003146, "right_eye_curve_mean": 0.002944
        }
        with open(os.path.join(cpp_kits_dir, "calidata.json"), 'w') as f:
            json.dump(cali_config, f)

        # 创建主配置
        main_config = {
            "remote_service": {"ip": "***********", "port": 1180},
            "cpp_program": {"directory": "./sc3e_r_cpprelated", "executable": "test_dms_internal_postmortem", "fallback_directory": "."},
            "processing": {"temp_dir": "./temp"},
            "paths": {
                "ip_port_config": "./cpp_kits/ip_port.json",
                "calibration_config": "./cpp_kits/calidata.json"
            }
        }
        with open(os.path.join(self.temp_dir, "end2end_config.json"), 'w') as f:
            json.dump(main_config, f)


if __name__ == '__main__':
    unittest.main(verbosity=2)
