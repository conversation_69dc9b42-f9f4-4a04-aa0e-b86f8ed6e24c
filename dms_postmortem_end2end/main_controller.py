#!/usr/bin/env python3
"""
DMS端到端自动化系统主控脚本
提供统一的命令行接口，协调所有模块，执行完整的端到端流程
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_manager import ConfigManager
from remote_service_manager import RemoteServiceManager
from system_logger import SystemLogger
from end_to_end_processor import EndToEndProcessor

# 版本信息
VERSION = "1.0.0"
PROGRAM_NAME = "DMS端到端自动化系统"


def setup_logging(log_level: str = "INFO") -> None:
    """设置基础日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper(), logging.INFO),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def validate_input_params(args) -> bool:
    """
    验证输入参数
    
    Args:
        args: 命令行参数
        
    Returns:
        参数是否有效
    """
    # 验证输入视频文件
    if not os.path.exists(args.input_video):
        print(f"错误: 输入视频文件不存在: {args.input_video}")
        return False
    
    # 验证时间范围格式
    if not args.time_ranges:
        print("错误: 必须指定时间范围")
        return False
    
    # 验证输出目录
    if args.output_dir:
        try:
            os.makedirs(args.output_dir, exist_ok=True)
        except Exception as e:
            print(f"错误: 无法创建输出目录 {args.output_dir}: {e}")
            return False
    
    return True


def main_pipeline(args) -> int:
    """
    主要处理流程
    
    Args:
        args: 命令行参数
        
    Returns:
        退出码
    """
    try:
        # 1. 初始化阶段
        print(f"\n🚀 {PROGRAM_NAME} v{VERSION}")
        print("=" * 60)
        
        logger = SystemLogger(log_file=args.log_file, log_level=args.log_level)
        config_manager = ConfigManager(args.config, args.base_dir)
        service_manager = RemoteServiceManager(config_manager)
        processor = EndToEndProcessor(config_manager, logger)
        
        logger.log_step("INITIALIZATION", "SUCCESS", "所有模块初始化完成")
        
        # 2. 预检查阶段
        logger.log_step("CONFIG_VALIDATION", "START", "验证配置文件")
        if not config_manager.validate_configs():
            logger.log_step("CONFIG_VALIDATION", "FAILED", "配置验证失败")
            return 1
        logger.log_step("CONFIG_VALIDATION", "SUCCESS", "配置验证通过")
        
        # 检查远端服务状态（如果启用）
        if not args.skip_service_check:
            logger.log_step("SERVICE_CHECK", "START", "检查远端服务状态")
            service_status = service_manager.check_service_status()
            
            if not service_status:
                logger.log_step("SERVICE_CHECK", "WARNING", "远端服务不可用")
                if args.require_service:
                    logger.log_step("SERVICE_CHECK", "FAILED", "要求远端服务可用但检查失败")
                    return 1
            else:
                logger.log_step("SERVICE_CHECK", "SUCCESS", "远端服务状态正常")
        
        # 3. 执行阶段
        input_params = {
            "input_video": args.input_video,
            "time_ranges": args.time_ranges,
            "roi": args.roi,
            "output_dir": args.output_dir or config_manager.get_config("processing.output_dir", "./output")
        }
        
        logger.log_step("PIPELINE_EXECUTION", "START", "开始端到端处理流程")
        result = processor.execute_full_pipeline(input_params)
        
        # 4. 结果处理
        if result["success"]:
            logger.log_step("PIPELINE_EXECUTION", "SUCCESS", f"处理完成，输出: {result['final_output']}")
            
            print("\n✅ 处理成功完成！")
            print(f"📁 输出文件: {result['final_output']}")
            print(f"🎬 处理片段数: {result['cut_segments']}")
            
            # 生成报告
            if not args.no_report:
                report = logger.generate_report()
                print(f"\n📊 执行报告已生成")
            
            return 0
        else:
            logger.log_step("PIPELINE_EXECUTION", "FAILED", f"处理失败: {result['error']}")
            
            print(f"\n❌ 处理失败: {result['error']}")
            
            # 生成错误报告
            report = logger.generate_report()
            print(f"\n📊 错误报告已生成")
            
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断处理")
        if 'logger' in locals():
            logger.log_step("PIPELINE_EXECUTION", "FAILED", "用户中断")
            logger.generate_report()
        return 130
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        if 'logger' in locals():
            logger.log_error(e, "MAIN_PIPELINE")
            logger.generate_report()
        return 1


def create_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description=f'{PROGRAM_NAME} - 整合视频裁剪和DMS处理的端到端自动化解决方案',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 基本用法
  python main_controller.py \\
    --input-video /path/to/video.mkv \\
    --time-ranges "00:04:57-00:05:17;00:15:10-00:15:30" \\
    --output-dir /path/to/output

  # 带ROI裁剪
  python main_controller.py \\
    --input-video /path/to/video.mkv \\
    --time-ranges "00:04:57-00:05:17" \\
    --roi "1920:1080:0:0" \\
    --output-dir /path/to/output

  # 跳过服务检查
  python main_controller.py \\
    --input-video /path/to/video.mkv \\
    --time-ranges "00:04:57-00:05:17" \\
    --skip-service-check
        """
    )
    
    # 必需参数
    parser.add_argument(
        '--input-video', '-i',
        required=True,
        help='输入视频文件路径'
    )
    
    parser.add_argument(
        '--time-ranges', '-t',
        required=True,
        help='时间范围，格式: "start1-end1;start2-end2"，如 "00:04:57-00:05:17;00:15:10-00:15:30"'
    )
    
    # 可选参数
    parser.add_argument(
        '--output-dir', '-o',
        help='输出目录路径（默认使用配置文件中的设置）'
    )
    
    parser.add_argument(
        '--roi',
        help='感兴趣区域，格式: width:height:x:y，如 "1920:1080:0:0"'
    )
    
    parser.add_argument(
        '--config', '-c',
        default='end2end_config.json',
        help='配置文件路径（默认: end2end_config.json）'
    )
    
    parser.add_argument(
        '--base-dir',
        help='基础目录路径（默认: 当前脚本目录）'
    )
    
    parser.add_argument(
        '--log-file',
        default='dms_end2end.log',
        help='日志文件路径（默认: dms_end2end.log）'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别（默认: INFO）'
    )
    
    # 服务相关选项
    parser.add_argument(
        '--skip-service-check',
        action='store_true',
        help='跳过远端服务状态检查'
    )
    
    parser.add_argument(
        '--require-service',
        action='store_true',
        help='要求远端服务必须可用'
    )
    
    # 其他选项
    parser.add_argument(
        '--no-report',
        action='store_true',
        help='不生成执行报告'
    )
    
    parser.add_argument(
        '--version', '-v',
        action='version',
        version=f'{PROGRAM_NAME} version {VERSION}'
    )
    
    return parser


def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    # 设置基础日志
    setup_logging(args.log_level)
    
    # 验证输入参数
    if not validate_input_params(args):
        return 1
    
    # 执行主要流程
    return main_pipeline(args)


if __name__ == "__main__":
    sys.exit(main())
